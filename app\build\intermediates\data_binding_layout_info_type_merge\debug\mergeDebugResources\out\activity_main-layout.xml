<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.ainative.mountainsurvival" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="184" endOffset="51"/></Target><Target id="@+id/statusBarLayout" view="LinearLayout"><Expressions/><location startLine="31" startOffset="4" endLine="94" endOffset="18"/></Target><Target id="@+id/warmthTextView" view="TextView"><Expressions/><location startLine="47" startOffset="8" endLine="56" endOffset="38"/></Target><Target id="@+id/staminaTextView" view="TextView"><Expressions/><location startLine="59" startOffset="8" endLine="68" endOffset="38"/></Target><Target id="@+id/firewoodTextView" view="TextView"><Expressions/><location startLine="71" startOffset="8" endLine="80" endOffset="38"/></Target><Target id="@+id/foodTextView" view="TextView"><Expressions/><location startLine="83" startOffset="8" endLine="92" endOffset="38"/></Target><Target id="@+id/storyScrollView" view="ScrollView"><Expressions/><location startLine="97" startOffset="4" endLine="128" endOffset="16"/></Target><Target id="@+id/storyTextView" view="TextView"><Expressions/><location startLine="113" startOffset="8" endLine="126" endOffset="38"/></Target><Target id="@+id/choicesLayout" view="LinearLayout"><Expressions/><location startLine="131" startOffset="4" endLine="182" endOffset="18"/></Target><Target id="@+id/choice1Button" view="Button"><Expressions/><location startLine="144" startOffset="8" endLine="154" endOffset="38"/></Target><Target id="@+id/choice2Button" view="Button"><Expressions/><location startLine="157" startOffset="8" endLine="167" endOffset="38"/></Target><Target id="@+id/choice3Button" view="Button"><Expressions/><location startLine="170" startOffset="8" endLine="180" endOffset="39"/></Target></Targets></Layout>