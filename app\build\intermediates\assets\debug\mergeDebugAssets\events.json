{"events": [{"id": "day1_start", "text": "刺骨的寒风把你从昏迷中唤醒。你挣扎着睁开眼，发现自己正躺在一间破旧的小木屋里。屋外，暴风雪的呼啸声如同怪物的嘶吼。你检查了一下自己的状况，必须立刻想办法生火！", "choices": [{"text": "劈开旧家具生火", "effects": {"stamina": -10, "firewood": 10, "cabin_integrity": -5}, "resultText": "你用墙角的斧头劈开了一把摇摇欲坠的椅子，冰冷的木屋里终于有了一丝暖意。虽然破坏了一些家具，但现在生存更重要。", "nextEventId": "day1_evening"}, {"text": "冒着风雪出去找柴火", "effects": {"warmth": -20, "stamina": -20, "firewood": 15}, "resultText": "你推开门，几乎被风雪掀翻。你在及膝深的雪地里艰难地收集了一些干树枝，冻僵的手指已经失去了知觉。但你收集到了更多的柴火。", "nextEventId": "day1_evening"}]}, {"id": "day1_evening", "text": "黄昏时分，橘红色的火光在壁炉中跳跃着。你感到一阵疲惫，但至少现在有了温暖。夜晚即将来临，你需要为漫长的黑夜做准备。", "choices": [{"text": "加固房屋抵御风雪", "effects": {"stamina": -15, "firewood": -3, "cabin_integrity": 15}, "requirements": {"firewood": 3, "stamina": 15}, "resultText": "你用一些木柴和找到的破布加固了窗户和门缝。虽然消耗了一些资源，但小屋变得更加坚固了。", "nextEventId": "day1_night"}, {"text": "早点休息保存体力", "effects": {"hope": 5}, "resultText": "你决定早点休息。躺在简陋的床上，听着外面的风雪声，你告诉自己一定要坚持下去。", "nextEventId": "day1_night"}]}, {"id": "day1_night", "text": "夜幕降临，小屋外的风雪声更加猛烈。壁炉中的火焰是你唯一的光明和温暖来源。你蜷缩在火炉旁，准备度过这个漫长的夜晚。", "effects": {"currentDay": 1}, "choices": [{"text": "继续下一天", "effects": {}, "resultText": "经过一夜的煎熬，你终于看到了第二天的曙光...", "nextEventId": "day2_start"}]}, {"id": "day2_start", "text": "火焰在壁炉里噼啪作响，你活过了第一个夜晚。但你知道，这仅仅是开始。你必须为接下来的日子做准备。透过结霜的窗户，你看到外面依然是白茫茫的一片。", "choices": [{"text": "探索小屋寻找物资", "effects": {"stamina": -10, "food": 2, "hope": 5, "currentDay": 1}, "resultText": "你仔细搜索小屋的每个角落。在一个角落里，你发现了几个生锈的罐头！虽然标签已经模糊，但这可能是珍贵的食物。你小心地打开一个罐头，里面是一些肉类。虽然味道有些奇怪，但在这种情况下已经是珍贵的食物了。", "nextEventId": "day2_night"}, {"text": "外出砍柴", "effects": {"warmth": -15, "stamina": -20, "firewood": 15, "currentDay": 1}, "requirements": {"stamina": 20}, "resultText": "你再次冒着严寒外出砍柴。虽然很辛苦，但你成功收集到了更多的燃料。", "nextEventId": "day2_night"}, {"text": "休息以保存体力", "effects": {"hope": -5, "currentDay": 1}, "resultText": "你决定在屋内休息，虽然没有做什么事情，但至少保存了体力。无所事事让你感到有些沮丧。", "nextEventId": "day2_night"}]}, {"id": "day3_start", "text": "第三天 - 呼啸的风\n\n风声变得更加尖锐，仿佛要撕裂这间小屋。你感到一阵不安，屋顶的积雪似乎太厚了。", "choices": [{"text": "加固小屋", "effects": {"stamina": -15, "firewood": -5, "cabin_integrity": 20, "currentDay": 1}, "requirements": {"stamina": 15, "firewood": 5}, "resultText": "你用尽全力加固了小屋的结构，虽然很累，但现在感觉更安全了。", "nextEventId": "day3_night"}, {"text": "尝试捕猎", "effects": {"warmth": -20, "stamina": -30, "food": 3, "currentDay": 1}, "requirements": {"stamina": 30}, "resultText": "你冒着严寒外出捕猎。经过几个小时的努力，你幸运地捕获了一只小动物！虽然消耗了很多体力和体温，但获得了珍贵的食物。", "nextEventId": "day3_night"}, {"text": "继续休息", "effects": {"currentDay": 1}, "resultText": "你选择保存体力，在屋内休息。", "nextEventId": "day3_night"}]}, {"id": "day3_night", "text": "第三个夜晚来临了。风声更加猛烈，小屋在狂风中摇摆。你紧紧抱着自己，希望能撑过这一夜。外面的暴风雪似乎永远不会停止。", "effects": {"currentDay": 1}, "choices": [{"text": "继续下一天", "effects": {}, "resultText": "第三夜过去了，你依然活着...", "nextEventId": "day4_start"}]}, {"id": "day4_start", "text": "第四天 - 希望的微光\n\n风雪似乎短暂地减弱了。透过窗户的缝隙，你似乎看到了远处有什么东西在反光。", "choices": [{"text": "前往反光点调查", "effects": {"warmth": -25, "stamina": -30, "currentDay": 1}, "requirements": {"stamina": 30}, "resultText": "你决定冒险外出调查那个反光点。深一脚浅一脚地跋涉过去，你发现那是一架小型飞机的残骸！机舱里一片狼藉。", "nextEventId": "crashed_plane"}, {"text": "这是陷阱，待在屋里", "effects": {"hope": -10, "currentDay": 1}, "resultText": "你觉得这可能是陷阱，选择待在屋里。虽然安全，但希望也在减少。", "nextEventId": "day4_night"}]}, {"id": "crashed_plane", "text": "坠毁的飞机\n\n你深一脚浅一脚地跋涉过去，发现那是一架小型飞机的残骸！机舱里一片狼藉。", "choices": [{"text": "搜索驾驶舱", "effects": {}, "resultText": "你仔细搜索驾驶舱，在座椅下面找到了一把信号枪！这可能是你获救的关键。", "specialItemGained": "信号枪", "nextEventId": "day4_night"}, {"text": "搜索货仓", "effects": {"food": 3}, "resultText": "你搜索了货仓，发现了一些应急食品！虽然包装有些破损，但还是可以食用的。", "nextEventId": "day4_night"}]}, {"id": "day4_night", "text": "第四个夜晚。今天的发现让你对明天充满了期待，但现在你必须先撑过这一夜。你握着手中的信号枪，这给了你一丝希望。", "effects": {"currentDay": 1}, "choices": [{"text": "继续下一天", "effects": {}, "resultText": "第四夜过去了，你离救援更近了...", "nextEventId": "day5_start"}]}, {"id": "day2_night", "text": "第二个夜晚来临了。你已经更加熟悉这间小屋，也更加了解如何在这里生存。壁炉中的火焰依然温暖，但你知道每一夜都是对生存意志的考验。", "effects": {"currentDay": 1}, "choices": [{"text": "继续下一天", "effects": {}, "resultText": "又一个夜晚过去了，你离救援又近了一步...", "nextEventId": "day3_start"}]}, {"id": "day5_start", "text": "第五天 - 绝望\n\n风雪再次加强，比以往任何时候都要猛烈。小屋在狂风中颤抖，你感到一阵深深的绝望。", "choices": [{"text": "检查房屋状况", "effects": {"stamina": -10}, "resultText": "你仔细检查了房屋的状况...", "nextEventId": "roof_leak_check"}, {"text": "蜷缩在壁炉边", "effects": {"hope": -5, "currentDay": 1}, "resultText": "你选择蜷缩在壁炉边取暖，虽然身体暖和了一些，但心情更加沮丧。", "nextEventId": "day5_night"}]}, {"id": "roof_leak_check", "text": "检查房屋状况", "choices": [], "conditionalEvents": {"cabin_integrity < 50": "roof_leak", "default": "house_ok"}}, {"id": "roof_leak", "text": "屋顶漏雪\n\n屋顶的一角开始漏下冰冷的雪水，很快就会让你的体温迅速流失！", "choices": [{"text": "立刻修复", "effects": {"stamina": -20, "firewood": -10, "cabin_integrity": 15, "currentDay": 1}, "requirements": {"stamina": 20, "firewood": 10}, "resultText": "你立刻动手修复屋顶，虽然消耗了大量体力和木柴，但阻止了进一步的损坏。", "nextEventId": "day5_night"}, {"text": "暂时不管", "effects": {"hope": -10, "currentDay": 1}, "resultText": "你决定暂时不管漏雪的问题，但这意味着每晚你都会损失更多体温。", "nextEventId": "day5_night", "specialEffects": {"roofLeaking": true}}]}, {"id": "house_ok", "text": "幸运的是，房屋状况还算良好，没有发现严重的问题。", "choices": [{"text": "继续", "effects": {}, "nextEventId": "day5_night"}]}, {"id": "day5_night", "text": "第五个夜晚。风雪依然猛烈，但你的意志依然坚强。", "effects": {"currentDay": 1}, "choices": [{"text": "继续生存之旅", "effects": {}, "resultText": "第五夜过去了，你还在坚持...", "nextEventId": "day6_start"}]}, {"id": "day6_start", "text": "第六天 - 漫长的夜晚\n\n这可能是最后一个夜晚了，也可能是你人生的最后一个夜晚。你必须撑过去。", "choices": [{"text": "把所有能烧的都烧了", "effects": {"firewood": 20, "cabin_integrity": -20, "hope": 10}, "resultText": "你把所有能烧的东西都当作燃料，包括一些家具。虽然破坏了房屋，但获得了大量木柴和一丝希望。", "nextEventId": "day6_evening"}, {"text": "做最后的准备", "effects": {"stamina": 10}, "resultText": "你冷静地为可能的最后一夜做准备，保存体力。", "nextEventId": "day6_evening"}]}, {"id": "day6_evening", "text": "第六天黄昏\n\n太阳即将落下，这可能是你看到的最后一次日落。但你不会放弃。", "choices": [{"text": "准备过夜", "effects": {}, "resultText": "你为可能的最后一夜做好了准备。", "nextEventId": "day6_night"}]}, {"id": "day6_night", "text": "第六个夜晚。这是最漫长、最寒冷的一夜。你紧紧抱着自己，等待黎明的到来。", "effects": {"currentDay": 1}, "choices": [{"text": "继续生存之旅", "effects": {}, "resultText": "第六夜过去了，你奇迹般地活了下来...", "nextEventId": "day7_start"}]}, {"id": "day7_start", "text": "第七天 - 最后的坚持\n\n你几乎已经麻木了。生存的本能驱使着你睁开眼睛。窗外依旧是白茫茫的一片。", "choices": [{"text": "等待", "effects": {}, "resultText": "你选择静静等待，保存最后的力气。", "nextEventId": "day7_evening"}, {"text": "如果有信号枪，向天空发射", "effects": {"hope": 50}, "requirements": {"special_items": ["signal_gun"]}, "resultText": "你拿出信号枪，向天空发射了一发信号弹！红色的光芒划破天际，这是你最后的希望！", "specialItemUsed": "signal_gun", "nextEventId": "rescue_ending"}]}, {"id": "day7_evening", "text": "第七天黄昏\n\n又一天过去了。你不知道还能坚持多久，但你依然活着。", "choices": [{"text": "准备过夜", "effects": {}, "resultText": "你为第七个夜晚做好了准备。", "nextEventId": "day7_night"}]}, {"id": "day7_night", "text": "第七个夜晚。你已经在这里生存了一周。无论结果如何，你都已经证明了自己的坚强。", "effects": {"currentDay": 1}, "choices": [{"text": "继续等待救援", "effects": {}, "resultText": "第七夜过去了，你依然在等待...", "nextEventId": "survival_ending"}]}, {"id": "rescue_ending", "text": "救援结局\n\n几个小时后，你听到了直升机的声音！信号枪起作用了！救援队找到了你，你获救了！", "choices": [{"text": "重新开始游戏", "effects": {}, "nextEventId": "day1_start"}]}, {"id": "survival_ending", "text": "生存结局\n\n你在雪山中坚持了七天七夜。虽然救援还没有到来，但你证明了人类的坚韧不拔。故事还在继续...", "choices": [{"text": "重新开始游戏", "effects": {}, "nextEventId": "day1_start"}]}]}