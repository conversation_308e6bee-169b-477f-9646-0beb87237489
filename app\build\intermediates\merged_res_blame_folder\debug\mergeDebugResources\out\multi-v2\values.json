{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-mergeDebugResources-51:\\values\\values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ee81e9003baef06be7850ff4f00325b8\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "279,375,376,391,392,418,419,533,534,535,536,537,538,539,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,572,573,574,620,621,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,658,688,689,690,691,692,693,694,772,2160,2161,2165,2166,2170,2327,2328", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12540,16728,16800,17941,18006,19576,19645,27126,27196,27264,27336,27406,27467,27541,28398,28459,28520,28582,28646,28708,28769,28837,28937,28997,29063,29136,29205,29262,29314,29829,29901,29977,32661,32696,33088,33143,33206,33261,33319,33377,33438,33501,33558,33609,33659,33720,33777,33843,33877,33912,34664,36737,36804,36876,36945,37014,37088,37160,42592,134559,134676,134877,134987,135188,147158,147230", "endLines": "279,375,376,391,392,418,419,533,534,535,536,537,538,539,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,572,573,574,620,621,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,658,688,689,690,691,692,693,694,772,2160,2164,2165,2169,2170,2327,2328", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "12595,16795,16883,18001,18067,19640,19703,27191,27259,27331,27401,27462,27536,27609,28454,28515,28577,28641,28703,28764,28832,28932,28992,29058,29131,29200,29257,29309,29371,29896,29972,30037,32691,32726,33138,33201,33256,33314,33372,33433,33496,33553,33604,33654,33715,33772,33838,33872,33907,33942,34729,36799,36871,36940,37009,37083,37155,37243,42658,134671,134872,134982,135183,135312,147225,147292"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\eb80c39fd83dc09184ca43a2c381fdcb\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "649", "startColumns": "4", "startOffsets": "34127", "endColumns": "53", "endOffsets": "34176"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "687", "startColumns": "4", "startOffsets": "36683", "endColumns": "53", "endOffsets": "36732"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8b176e8aba06099230d4846cfebebfa3\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "657,702,703,704,705,706,707,708,709,710,711,714,715,716,717,718,719,720,721,722,723,724,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,1869,1879", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "34591,37704,37792,37878,37959,38043,38112,38177,38260,38366,38452,38572,38626,38695,38756,38825,38914,39009,39083,39180,39273,39371,39520,39611,39699,39795,39893,39957,40025,40112,40206,40273,40345,40417,40518,40627,40703,40772,40820,40886,40950,41024,41081,41138,41210,41260,41314,41385,41456,41526,41595,41653,41729,41800,41874,41960,42010,42080,112499,113214", "endLines": "657,702,703,704,705,706,707,708,709,710,713,714,715,716,717,718,719,720,721,722,723,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,1878,1881", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "34659,37787,37873,37954,38038,38107,38172,38255,38361,38447,38567,38621,38690,38751,38820,38909,39004,39078,39175,39268,39366,39515,39606,39694,39790,39888,39952,40020,40107,40201,40268,40340,40412,40513,40622,40698,40767,40815,40881,40945,41019,41076,41133,41205,41255,41309,41380,41451,41521,41590,41648,41724,41795,41869,41955,42005,42075,42140,113209,113362"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "374,379,382,399,400,420,421,430,431,432,439,444,445,446,447,450,451", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16680,17055,17258,18473,18522,19708,19755,20395,20442,20489,20947,21281,21326,21371,21418,21588,21635", "endColumns": "47,50,41,48,44,46,51,46,46,46,47,44,44,46,48,46,41", "endOffsets": "16723,17101,17295,18517,18562,19750,19802,20437,20484,20531,20990,21321,21366,21413,21462,21630,21672"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\09a3a233c8f6de7d440dffb5c239312f\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "619,623", "startColumns": "4,4", "startOffsets": "32607,32772", "endColumns": "53,66", "endOffsets": "32656,32834"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "57", "endColumns": "56", "endOffsets": "109"}, "to": {"startLines": "405", "startColumns": "4", "startOffsets": "18847", "endColumns": "56", "endOffsets": "18899"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2207", "startColumns": "4", "startOffsets": "137953", "endLines": "2219", "endColumns": "12", "endOffsets": "138494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3684f126870460cbf44e1e8c4b7b80a6\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "686", "startColumns": "4", "startOffsets": "36600", "endColumns": "82", "endOffsets": "36678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\486d1f81ed2f2b3c0dad55df8e8c8c75\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "63,124,125,318,365,366,367,368,369,370,371,372,373,377,378,380,381,383,384,385,386,387,388,389,390,393,394,395,396,397,398,401,402,403,404,406,407,408,409,410,411,412,413,414,415,416,417,422,423,424,425,426,427,428,429,433,434,435,436,437,438,440,441,442,443,448,449,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,540,541,542,543,544,545,546,547,548,564,565,566,567,568,569,570,571,607,608,609,610,617,624,625,628,645,653,654,655,656,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,767,778,779,780,781,782,783,791,792,796,800,804,809,815,822,826,830,835,839,843,847,851,855,859,865,869,875,879,885,889,894,898,901,905,911,915,921,925,931,934,938,942,946,950,954,955,956,957,960,963,966,969,973,974,975,976,977,980,982,984,986,991,992,996,1002,1006,1007,1009,1021,1022,1026,1032,1036,1037,1038,1042,1069,1073,1074,1078,1106,1278,1304,1475,1501,1532,1540,1546,1562,1584,1589,1594,1604,1613,1622,1626,1633,1652,1659,1660,1669,1672,1675,1679,1683,1687,1690,1691,1696,1701,1711,1716,1723,1729,1730,1733,1737,1742,1744,1746,1749,1752,1754,1758,1761,1768,1771,1774,1778,1780,1784,1786,1788,1790,1794,1802,1810,1822,1828,1837,1840,1851,1854,1855,1860,1861,1889,1958,2028,2029,2039,2048,2049,2051,2055,2058,2061,2064,2067,2070,2073,2076,2080,2083,2086,2089,2093,2096,2100,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2121,2122,2123,2124,2126,2128,2129,2130,2131,2132,2133,2134,2135,2137,2138,2140,2141,2143,2145,2146,2148,2149,2150,2151,2152,2153,2155,2156,2157,2158,2159,2171,2173,2175,2177,2178,2179,2180,2181,2182,2183,2184,2185,2186,2187,2188,2189,2191,2192,2193,2194,2195,2196,2197,2199,2203,2220,2221,2222,2223,2224,2225,2229,2230,2231,2232,2234,2236,2238,2240,2242,2243,2244,2245,2247,2249,2251,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2265,2266,2267,2268,2270,2272,2273,2275,2276,2278,2280,2282,2283,2284,2285,2286,2287,2288,2289,2290,2291,2292,2293,2295,2296,2297,2298,2300,2301,2302,2303,2304,2306,2308,2310,2312,2313,2314,2315,2316,2317,2318,2319,2320,2321,2322,2323,2324,2325,2326", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2809,5350,5395,14217,16062,16117,16179,16243,16313,16374,16449,16525,16602,16888,16973,17106,17182,17300,17377,17455,17561,17667,17746,17826,17883,18072,18146,18221,18286,18352,18412,18567,18639,18712,18779,18904,18963,19022,19081,19140,19199,19253,19307,19360,19414,19468,19522,19807,19881,19960,20033,20107,20178,20250,20322,20536,20593,20651,20724,20798,20872,20995,21067,21140,21210,21467,21527,21677,21746,21815,21885,21959,22035,22099,22176,22252,22329,22394,22463,22540,22615,22684,22752,22829,22895,22956,23053,23118,23187,23286,23357,23416,23474,23531,23590,23654,23725,23797,23869,23941,24013,24080,24148,24216,24275,24338,24402,24492,24583,24643,24709,24776,24842,24912,24976,25029,25096,25157,25224,25337,25395,25458,25523,25588,25663,25736,25808,25852,25899,25945,25994,26055,26116,26177,26239,26303,26367,26431,26496,26559,26619,26680,26746,26805,26865,26927,26998,27058,27614,27700,27787,27877,27964,28052,28134,28217,28307,29376,29428,29486,29531,29597,29661,29718,29775,31952,32009,32057,32106,32518,32839,32886,33042,33947,34348,34412,34474,34534,34734,34808,34878,34956,35010,35080,35165,35213,35259,35320,35383,35449,35513,35584,35647,35712,35776,35837,35898,35950,36023,36097,36166,36241,36315,36389,36530,42365,42947,43025,43115,43203,43299,43389,43971,44060,44307,44588,44840,45125,45518,45995,46217,46439,46715,46942,47172,47402,47632,47862,48089,48508,48734,49159,49389,49817,50036,50319,50527,50658,50885,51311,51536,51963,52184,52609,52729,53005,53306,53630,53921,54235,54372,54503,54608,54850,55017,55221,55429,55700,55812,55924,56029,56146,56360,56506,56646,56732,57080,57168,57414,57832,58081,58163,58261,58918,59018,59270,59694,59949,60043,60132,60369,62393,62635,62737,62990,65146,75827,77343,88038,89566,91323,91949,92369,93630,94895,95151,95387,95934,96428,97033,97231,97811,99179,99554,99672,100210,100367,100563,100836,101092,101262,101403,101467,101832,102199,102875,103139,103477,103830,103924,104110,104416,104678,104803,104930,105169,105380,105499,105692,105869,106324,106505,106627,106886,106999,107186,107288,107395,107524,107799,108307,108803,109680,109974,110544,110693,111425,111597,111681,112017,112109,113673,118904,124275,124337,124915,125499,125590,125703,125932,126092,126244,126415,126581,126750,126917,127080,127323,127493,127666,127837,128111,128310,128515,128845,128929,129025,129121,129219,129319,129421,129523,129625,129727,129829,129929,130025,130137,130266,130389,130520,130651,130749,130863,130957,131097,131231,131327,131439,131539,131655,131751,131863,131963,132103,132239,132403,132533,132691,132841,132982,133126,133261,133373,133523,133651,133779,133915,134047,134177,134307,134419,135317,135463,135607,135745,135811,135901,135977,136081,136171,136273,136381,136489,136589,136669,136761,136859,136969,137021,137099,137205,137297,137401,137511,137633,137796,138499,138579,138679,138769,138879,138969,139210,139304,139410,139502,139602,139714,139828,139944,140060,140154,140268,140380,140482,140602,140724,140806,140910,141030,141156,141254,141348,141436,141548,141664,141786,141898,142073,142189,142275,142367,142479,142603,142670,142796,142864,142992,143136,143264,143333,143428,143543,143656,143755,143864,143975,144086,144187,144292,144392,144522,144613,144736,144830,144942,145028,145132,145228,145316,145434,145538,145642,145768,145856,145964,146064,146154,146264,146348,146450,146534,146588,146652,146758,146844,146954,147038", "endLines": "63,124,125,318,365,366,367,368,369,370,371,372,373,377,378,380,381,383,384,385,386,387,388,389,390,393,394,395,396,397,398,401,402,403,404,406,407,408,409,410,411,412,413,414,415,416,417,422,423,424,425,426,427,428,429,433,434,435,436,437,438,440,441,442,443,448,449,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,540,541,542,543,544,545,546,547,548,564,565,566,567,568,569,570,571,607,608,609,610,617,624,625,628,645,653,654,655,656,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,767,778,779,780,781,782,790,791,795,799,803,808,814,821,825,829,834,838,842,846,850,854,858,864,868,874,878,884,888,893,897,900,904,910,914,920,924,930,933,937,941,945,949,953,954,955,956,959,962,965,968,972,973,974,975,976,979,981,983,985,990,991,995,1001,1005,1006,1008,1020,1021,1025,1031,1035,1036,1037,1041,1068,1072,1073,1077,1105,1277,1303,1474,1500,1531,1539,1545,1561,1583,1588,1593,1603,1612,1621,1625,1632,1651,1658,1659,1668,1671,1674,1678,1682,1686,1689,1690,1695,1700,1710,1715,1722,1728,1729,1732,1736,1741,1743,1745,1748,1751,1753,1757,1760,1767,1770,1773,1777,1779,1783,1785,1787,1789,1793,1801,1809,1821,1827,1836,1839,1850,1853,1854,1859,1860,1865,1957,2027,2028,2038,2047,2048,2050,2054,2057,2060,2063,2066,2069,2072,2075,2079,2082,2085,2088,2092,2095,2099,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2121,2122,2123,2125,2127,2128,2129,2130,2131,2132,2133,2134,2136,2137,2139,2140,2142,2144,2145,2147,2148,2149,2150,2151,2152,2154,2155,2156,2157,2158,2159,2172,2174,2176,2177,2178,2179,2180,2181,2182,2183,2184,2185,2186,2187,2188,2190,2191,2192,2193,2194,2195,2196,2198,2202,2206,2220,2221,2222,2223,2224,2228,2229,2230,2231,2233,2235,2237,2239,2241,2242,2243,2244,2246,2248,2250,2251,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2264,2265,2266,2267,2269,2271,2272,2274,2275,2277,2279,2281,2282,2283,2284,2285,2286,2287,2288,2289,2290,2291,2292,2294,2295,2296,2297,2299,2300,2301,2302,2303,2305,2307,2309,2311,2312,2313,2314,2315,2316,2317,2318,2319,2320,2321,2322,2323,2324,2325,2326", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "2859,5390,5439,14253,16112,16174,16238,16308,16369,16444,16520,16597,16675,16968,17050,17177,17253,17372,17450,17556,17662,17741,17821,17878,17936,18141,18216,18281,18347,18407,18468,18634,18707,18774,18842,18958,19017,19076,19135,19194,19248,19302,19355,19409,19463,19517,19571,19876,19955,20028,20102,20173,20245,20317,20390,20588,20646,20719,20793,20867,20942,21062,21135,21205,21276,21522,21583,21741,21810,21880,21954,22030,22094,22171,22247,22324,22389,22458,22535,22610,22679,22747,22824,22890,22951,23048,23113,23182,23281,23352,23411,23469,23526,23585,23649,23720,23792,23864,23936,24008,24075,24143,24211,24270,24333,24397,24487,24578,24638,24704,24771,24837,24907,24971,25024,25091,25152,25219,25332,25390,25453,25518,25583,25658,25731,25803,25847,25894,25940,25989,26050,26111,26172,26234,26298,26362,26426,26491,26554,26614,26675,26741,26800,26860,26922,26993,27053,27121,27695,27782,27872,27959,28047,28129,28212,28302,28393,29423,29481,29526,29592,29656,29713,29770,29824,32004,32052,32101,32152,32547,32881,32930,33083,33974,34407,34469,34529,34586,34803,34873,34951,35005,35075,35160,35208,35254,35315,35378,35444,35508,35579,35642,35707,35771,35832,35893,35945,36018,36092,36161,36236,36310,36384,36525,36595,42413,43020,43110,43198,43294,43384,43966,44055,44302,44583,44835,45120,45513,45990,46212,46434,46710,46937,47167,47397,47627,47857,48084,48503,48729,49154,49384,49812,50031,50314,50522,50653,50880,51306,51531,51958,52179,52604,52724,53000,53301,53625,53916,54230,54367,54498,54603,54845,55012,55216,55424,55695,55807,55919,56024,56141,56355,56501,56641,56727,57075,57163,57409,57827,58076,58158,58256,58913,59013,59265,59689,59944,60038,60127,60364,62388,62630,62732,62985,65141,75822,77338,88033,89561,91318,91944,92364,93625,94890,95146,95382,95929,96423,97028,97226,97806,99174,99549,99667,100205,100362,100558,100831,101087,101257,101398,101462,101827,102194,102870,103134,103472,103825,103919,104105,104411,104673,104798,104925,105164,105375,105494,105687,105864,106319,106500,106622,106881,106994,107181,107283,107390,107519,107794,108302,108798,109675,109969,110539,110688,111420,111592,111676,112012,112104,112382,118899,124270,124332,124910,125494,125585,125698,125927,126087,126239,126410,126576,126745,126912,127075,127318,127488,127661,127832,128106,128305,128510,128840,128924,129020,129116,129214,129314,129416,129518,129620,129722,129824,129924,130020,130132,130261,130384,130515,130646,130744,130858,130952,131092,131226,131322,131434,131534,131650,131746,131858,131958,132098,132234,132398,132528,132686,132836,132977,133121,133256,133368,133518,133646,133774,133910,134042,134172,134302,134414,134554,135458,135602,135740,135806,135896,135972,136076,136166,136268,136376,136484,136584,136664,136756,136854,136964,137016,137094,137200,137292,137396,137506,137628,137791,137948,138574,138674,138764,138874,138964,139205,139299,139405,139497,139597,139709,139823,139939,140055,140149,140263,140375,140477,140597,140719,140801,140905,141025,141151,141249,141343,141431,141543,141659,141781,141893,142068,142184,142270,142362,142474,142598,142665,142791,142859,142987,143131,143259,143328,143423,143538,143651,143750,143859,143970,144081,144182,144287,144387,144517,144608,144731,144825,144937,145023,145127,145223,145311,145429,145533,145637,145763,145851,145959,146059,146149,146259,146343,146445,146529,146583,146647,146753,146839,146949,147033,147153"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ffaf60804bb777caaab07cd66703845d\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,611,612,613,616,618,652,695,696,697,698,699,700,701,763,764,765,766,768,769,770,771,773,774,775,1866,1882,1885", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "30042,30101,30160,30220,30280,30340,30400,30460,30520,30580,30640,30700,30760,30819,30879,30939,30999,31059,31119,31179,31239,31299,31359,31419,31478,31538,31598,31657,31716,31775,31834,31893,32157,32231,32289,32467,32552,34295,37248,37313,37367,37433,37534,37592,37644,42145,42207,42261,42311,42418,42464,42510,42552,42663,42710,42746,112387,113367,113478", "endLines": "575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,611,612,613,616,618,652,695,696,697,698,699,700,701,763,764,765,766,768,769,770,771,773,774,775,1868,1884,1888", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "30096,30155,30215,30275,30335,30395,30455,30515,30575,30635,30695,30755,30814,30874,30934,30994,31054,31114,31174,31234,31294,31354,31414,31473,31533,31593,31652,31711,31770,31829,31888,31947,32226,32284,32339,32513,32602,34343,37308,37362,37428,37529,37587,37639,37699,42202,42256,42306,42360,42459,42505,42547,42587,42705,42741,42831,112494,113473,113668"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fe94774b27fdaa35093098e8a904795c\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "650", "startColumns": "4", "startOffsets": "34181", "endColumns": "49", "endOffsets": "34226"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fce910b49377672473080ebeba046646\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "776,777", "startColumns": "4,4", "startOffsets": "42836,42892", "endColumns": "55,54", "endOffsets": "42887,42942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0b77f7f969be2bb725302f33ce1d15eb\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "626,648", "startColumns": "4,4", "startOffsets": "32935,34067", "endColumns": "41,59", "endOffsets": "32972,34122"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7b5fdb1008b0acd05f15a7bb1e11c858\\transformed\\constraintlayout-2.1.4\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,9,10,15,16,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,42,43,44,45,55,64,65,66,71,72,77,82,83,84,89,90,95,96,101,102,103,109,110,111,116,122,123,126,127,133,134,135,136,139,142,145,146,149,152,153,154,155,156,159,162,163,164,165,171,176,179,182,183,184,189,190,191,194,197,198,201,204,207,210,211,212,215,218,219,224,225,231,236,239,242,243,244,245,246,247,248,249,250,251,252,253,269,275,276,277,278,280,287,293,294,295,298,303,304,312,313,314,315,316,317,319,320,329,330,331,337,338,344,348,349,350,351,352,361,622,646", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,434,490,676,737,1028,1080,1130,1183,1231,1282,1337,1397,1462,1521,1583,1635,1696,1758,1804,1937,1989,2039,2090,2497,2864,2909,2968,3165,3222,3417,3598,3652,3709,3901,3959,4155,4211,4405,4462,4513,4735,4787,4842,5032,5248,5298,5444,5500,5706,5767,5827,5897,6030,6161,6289,6357,6486,6612,6674,6737,6805,6872,6995,7120,7187,7252,7317,7606,7787,7908,8029,8095,8162,8372,8441,8507,8632,8758,8825,8951,9078,9203,9330,9386,9451,9577,9700,9765,9973,10040,10328,10508,10628,10748,10813,10875,10937,11001,11063,11122,11182,11243,11304,11363,11423,12083,12334,12385,12434,12482,12600,12892,13122,13169,13229,13335,13515,13569,13904,13958,14014,14060,14107,14158,14258,14310,14640,14699,14753,14991,15046,15248,15387,15433,15488,15533,15577,15925,32731,33979", "endLines": "8,9,14,15,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,41,42,43,44,54,62,64,65,70,71,76,81,82,83,88,89,94,95,100,101,102,108,109,110,115,121,122,123,126,132,133,134,135,138,141,144,145,148,151,152,153,154,155,158,161,162,163,164,170,175,178,181,182,183,188,189,190,193,196,197,200,203,206,209,210,211,214,217,218,223,224,230,235,238,241,242,243,244,245,246,247,248,249,250,251,252,268,274,275,276,277,278,286,292,293,294,297,302,303,311,312,313,314,315,316,317,319,328,329,330,336,337,343,347,348,349,350,351,360,364,622,646", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44", "endOffsets": "429,485,671,732,1023,1075,1125,1178,1226,1277,1332,1392,1457,1516,1578,1630,1691,1753,1799,1932,1984,2034,2085,2492,2804,2904,2963,3160,3217,3412,3593,3647,3704,3896,3954,4150,4206,4400,4457,4508,4730,4782,4837,5027,5243,5293,5345,5495,5701,5762,5822,5892,6025,6156,6284,6352,6481,6607,6669,6732,6800,6867,6990,7115,7182,7247,7312,7601,7782,7903,8024,8090,8157,8367,8436,8502,8627,8753,8820,8946,9073,9198,9325,9381,9446,9572,9695,9760,9968,10035,10323,10503,10623,10743,10808,10870,10932,10996,11058,11117,11177,11238,11299,11358,11418,12078,12329,12380,12429,12477,12535,12887,13117,13164,13224,13330,13510,13564,13899,13953,14009,14055,14102,14153,14212,14305,14635,14694,14748,14986,15041,15243,15382,15428,15483,15528,15572,15920,16057,32767,34019"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fd1fb44d3025048634a00a9f3a07ea5c\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "615", "startColumns": "4", "startOffsets": "32401", "endColumns": "65", "endOffsets": "32462"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b53b0a6e7beae069ef1b2e945452fd0d\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "647", "startColumns": "4", "startOffsets": "34024", "endColumns": "42", "endOffsets": "34062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\dad7540d7a0d6788347b647b05ce89b9\\transformed\\fragment-1.3.6\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "614,627,651", "startColumns": "4,4,4", "startOffsets": "32344,32977,34231", "endColumns": "56,64,63", "endOffsets": "32396,33037,34290"}}]}, {"outputFile": "com.ainative.mountainsurvival.app-mergeDebugResources-51:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ee81e9003baef06be7850ff4f00325b8\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "279,375,376,391,392,418,419,533,534,535,536,537,538,539,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,572,573,574,620,621,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,658,689,690,691,692,693,694,695,773,2161,2162,2166,2167,2171,2328,2329", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12540,16728,16800,17941,18006,19576,19645,27126,27196,27264,27336,27406,27467,27541,28398,28459,28520,28582,28646,28708,28769,28837,28937,28997,29063,29136,29205,29262,29314,29829,29901,29977,32661,32696,33088,33143,33206,33261,33319,33377,33438,33501,33558,33609,33659,33720,33777,33843,33877,33912,34664,36782,36849,36921,36990,37059,37133,37205,42637,134604,134721,134922,135032,135233,147203,147275", "endLines": "279,375,376,391,392,418,419,533,534,535,536,537,538,539,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,572,573,574,620,621,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,658,689,690,691,692,693,694,695,773,2161,2165,2166,2170,2171,2328,2329", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "12595,16795,16883,18001,18067,19640,19703,27191,27259,27331,27401,27462,27536,27609,28454,28515,28577,28641,28703,28764,28832,28932,28992,29058,29131,29200,29257,29309,29371,29896,29972,30037,32691,32726,33138,33201,33256,33314,33372,33433,33496,33553,33604,33654,33715,33772,33838,33872,33907,33942,34729,36844,36916,36985,37054,37128,37200,37288,42703,134716,134917,135027,135228,135357,147270,147337"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\eb80c39fd83dc09184ca43a2c381fdcb\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "649", "startColumns": "4", "startOffsets": "34127", "endColumns": "53", "endOffsets": "34176"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1,2", "startColumns": "4,4", "startOffsets": "16,58", "endColumns": "41,56", "endOffsets": "53,110"}, "to": {"startLines": "687,688", "startColumns": "4,4", "startOffsets": "36683,36725", "endColumns": "41,56", "endOffsets": "36720,36777"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8b176e8aba06099230d4846cfebebfa3\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "657,703,704,705,706,707,708,709,710,711,712,715,716,717,718,719,720,721,722,723,724,725,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,1870,1880", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "34591,37749,37837,37923,38004,38088,38157,38222,38305,38411,38497,38617,38671,38740,38801,38870,38959,39054,39128,39225,39318,39416,39565,39656,39744,39840,39938,40002,40070,40157,40251,40318,40390,40462,40563,40672,40748,40817,40865,40931,40995,41069,41126,41183,41255,41305,41359,41430,41501,41571,41640,41698,41774,41845,41919,42005,42055,42125,112544,113259", "endLines": "657,703,704,705,706,707,708,709,710,711,714,715,716,717,718,719,720,721,722,723,724,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,1879,1882", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "34659,37832,37918,37999,38083,38152,38217,38300,38406,38492,38612,38666,38735,38796,38865,38954,39049,39123,39220,39313,39411,39560,39651,39739,39835,39933,39997,40065,40152,40246,40313,40385,40457,40558,40667,40743,40812,40860,40926,40990,41064,41121,41178,41250,41300,41354,41425,41496,41566,41635,41693,41769,41840,41914,42000,42050,42120,42185,113254,113407"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "374,379,382,399,400,420,421,430,431,432,439,444,445,446,447,450,451", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16680,17055,17258,18473,18522,19708,19755,20395,20442,20489,20947,21281,21326,21371,21418,21588,21635", "endColumns": "47,50,41,48,44,46,51,46,46,46,47,44,44,46,48,46,41", "endOffsets": "16723,17101,17295,18517,18562,19750,19802,20437,20484,20531,20990,21321,21366,21413,21462,21630,21672"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\09a3a233c8f6de7d440dffb5c239312f\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "619,623", "startColumns": "4,4", "startOffsets": "32607,32772", "endColumns": "53,66", "endOffsets": "32656,32834"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "405", "startColumns": "4", "startOffsets": "18847", "endColumns": "56", "endOffsets": "18899"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2208", "startColumns": "4", "startOffsets": "137998", "endLines": "2220", "endColumns": "12", "endOffsets": "138539"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3684f126870460cbf44e1e8c4b7b80a6\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "686", "startColumns": "4", "startOffsets": "36600", "endColumns": "82", "endOffsets": "36678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\486d1f81ed2f2b3c0dad55df8e8c8c75\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "63,124,125,318,365,366,367,368,369,370,371,372,373,377,378,380,381,383,384,385,386,387,388,389,390,393,394,395,396,397,398,401,402,403,404,406,407,408,409,410,411,412,413,414,415,416,417,422,423,424,425,426,427,428,429,433,434,435,436,437,438,440,441,442,443,448,449,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,540,541,542,543,544,545,546,547,548,564,565,566,567,568,569,570,571,607,608,609,610,617,624,625,628,645,653,654,655,656,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,768,779,780,781,782,783,784,792,793,797,801,805,810,816,823,827,831,836,840,844,848,852,856,860,866,870,876,880,886,890,895,899,902,906,912,916,922,926,932,935,939,943,947,951,955,956,957,958,961,964,967,970,974,975,976,977,978,981,983,985,987,992,993,997,1003,1007,1008,1010,1022,1023,1027,1033,1037,1038,1039,1043,1070,1074,1075,1079,1107,1279,1305,1476,1502,1533,1541,1547,1563,1585,1590,1595,1605,1614,1623,1627,1634,1653,1660,1661,1670,1673,1676,1680,1684,1688,1691,1692,1697,1702,1712,1717,1724,1730,1731,1734,1738,1743,1745,1747,1750,1753,1755,1759,1762,1769,1772,1775,1779,1781,1785,1787,1789,1791,1795,1803,1811,1823,1829,1838,1841,1852,1855,1856,1861,1862,1890,1959,2029,2030,2040,2049,2050,2052,2056,2059,2062,2065,2068,2071,2074,2077,2081,2084,2087,2090,2094,2097,2101,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2121,2122,2123,2124,2125,2127,2129,2130,2131,2132,2133,2134,2135,2136,2138,2139,2141,2142,2144,2146,2147,2149,2150,2151,2152,2153,2154,2156,2157,2158,2159,2160,2172,2174,2176,2178,2179,2180,2181,2182,2183,2184,2185,2186,2187,2188,2189,2190,2192,2193,2194,2195,2196,2197,2198,2200,2204,2221,2222,2223,2224,2225,2226,2230,2231,2232,2233,2235,2237,2239,2241,2243,2244,2245,2246,2248,2250,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2263,2266,2267,2268,2269,2271,2273,2274,2276,2277,2279,2281,2283,2284,2285,2286,2287,2288,2289,2290,2291,2292,2293,2294,2296,2297,2298,2299,2301,2302,2303,2304,2305,2307,2309,2311,2313,2314,2315,2316,2317,2318,2319,2320,2321,2322,2323,2324,2325,2326,2327", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2809,5350,5395,14217,16062,16117,16179,16243,16313,16374,16449,16525,16602,16888,16973,17106,17182,17300,17377,17455,17561,17667,17746,17826,17883,18072,18146,18221,18286,18352,18412,18567,18639,18712,18779,18904,18963,19022,19081,19140,19199,19253,19307,19360,19414,19468,19522,19807,19881,19960,20033,20107,20178,20250,20322,20536,20593,20651,20724,20798,20872,20995,21067,21140,21210,21467,21527,21677,21746,21815,21885,21959,22035,22099,22176,22252,22329,22394,22463,22540,22615,22684,22752,22829,22895,22956,23053,23118,23187,23286,23357,23416,23474,23531,23590,23654,23725,23797,23869,23941,24013,24080,24148,24216,24275,24338,24402,24492,24583,24643,24709,24776,24842,24912,24976,25029,25096,25157,25224,25337,25395,25458,25523,25588,25663,25736,25808,25852,25899,25945,25994,26055,26116,26177,26239,26303,26367,26431,26496,26559,26619,26680,26746,26805,26865,26927,26998,27058,27614,27700,27787,27877,27964,28052,28134,28217,28307,29376,29428,29486,29531,29597,29661,29718,29775,31952,32009,32057,32106,32518,32839,32886,33042,33947,34348,34412,34474,34534,34734,34808,34878,34956,35010,35080,35165,35213,35259,35320,35383,35449,35513,35584,35647,35712,35776,35837,35898,35950,36023,36097,36166,36241,36315,36389,36530,42410,42992,43070,43160,43248,43344,43434,44016,44105,44352,44633,44885,45170,45563,46040,46262,46484,46760,46987,47217,47447,47677,47907,48134,48553,48779,49204,49434,49862,50081,50364,50572,50703,50930,51356,51581,52008,52229,52654,52774,53050,53351,53675,53966,54280,54417,54548,54653,54895,55062,55266,55474,55745,55857,55969,56074,56191,56405,56551,56691,56777,57125,57213,57459,57877,58126,58208,58306,58963,59063,59315,59739,59994,60088,60177,60414,62438,62680,62782,63035,65191,75872,77388,88083,89611,91368,91994,92414,93675,94940,95196,95432,95979,96473,97078,97276,97856,99224,99599,99717,100255,100412,100608,100881,101137,101307,101448,101512,101877,102244,102920,103184,103522,103875,103969,104155,104461,104723,104848,104975,105214,105425,105544,105737,105914,106369,106550,106672,106931,107044,107231,107333,107440,107569,107844,108352,108848,109725,110019,110589,110738,111470,111642,111726,112062,112154,113718,118949,124320,124382,124960,125544,125635,125748,125977,126137,126289,126460,126626,126795,126962,127125,127368,127538,127711,127882,128156,128355,128560,128890,128974,129070,129166,129264,129364,129466,129568,129670,129772,129874,129974,130070,130182,130311,130434,130565,130696,130794,130908,131002,131142,131276,131372,131484,131584,131700,131796,131908,132008,132148,132284,132448,132578,132736,132886,133027,133171,133306,133418,133568,133696,133824,133960,134092,134222,134352,134464,135362,135508,135652,135790,135856,135946,136022,136126,136216,136318,136426,136534,136634,136714,136806,136904,137014,137066,137144,137250,137342,137446,137556,137678,137841,138544,138624,138724,138814,138924,139014,139255,139349,139455,139547,139647,139759,139873,139989,140105,140199,140313,140425,140527,140647,140769,140851,140955,141075,141201,141299,141393,141481,141593,141709,141831,141943,142118,142234,142320,142412,142524,142648,142715,142841,142909,143037,143181,143309,143378,143473,143588,143701,143800,143909,144020,144131,144232,144337,144437,144567,144658,144781,144875,144987,145073,145177,145273,145361,145479,145583,145687,145813,145901,146009,146109,146199,146309,146393,146495,146579,146633,146697,146803,146889,146999,147083", "endLines": "63,124,125,318,365,366,367,368,369,370,371,372,373,377,378,380,381,383,384,385,386,387,388,389,390,393,394,395,396,397,398,401,402,403,404,406,407,408,409,410,411,412,413,414,415,416,417,422,423,424,425,426,427,428,429,433,434,435,436,437,438,440,441,442,443,448,449,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,540,541,542,543,544,545,546,547,548,564,565,566,567,568,569,570,571,607,608,609,610,617,624,625,628,645,653,654,655,656,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,768,779,780,781,782,783,791,792,796,800,804,809,815,822,826,830,835,839,843,847,851,855,859,865,869,875,879,885,889,894,898,901,905,911,915,921,925,931,934,938,942,946,950,954,955,956,957,960,963,966,969,973,974,975,976,977,980,982,984,986,991,992,996,1002,1006,1007,1009,1021,1022,1026,1032,1036,1037,1038,1042,1069,1073,1074,1078,1106,1278,1304,1475,1501,1532,1540,1546,1562,1584,1589,1594,1604,1613,1622,1626,1633,1652,1659,1660,1669,1672,1675,1679,1683,1687,1690,1691,1696,1701,1711,1716,1723,1729,1730,1733,1737,1742,1744,1746,1749,1752,1754,1758,1761,1768,1771,1774,1778,1780,1784,1786,1788,1790,1794,1802,1810,1822,1828,1837,1840,1851,1854,1855,1860,1861,1866,1958,2028,2029,2039,2048,2049,2051,2055,2058,2061,2064,2067,2070,2073,2076,2080,2083,2086,2089,2093,2096,2100,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2121,2122,2123,2124,2126,2128,2129,2130,2131,2132,2133,2134,2135,2137,2138,2140,2141,2143,2145,2146,2148,2149,2150,2151,2152,2153,2155,2156,2157,2158,2159,2160,2173,2175,2177,2178,2179,2180,2181,2182,2183,2184,2185,2186,2187,2188,2189,2191,2192,2193,2194,2195,2196,2197,2199,2203,2207,2221,2222,2223,2224,2225,2229,2230,2231,2232,2234,2236,2238,2240,2242,2243,2244,2245,2247,2249,2251,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2265,2266,2267,2268,2270,2272,2273,2275,2276,2278,2280,2282,2283,2284,2285,2286,2287,2288,2289,2290,2291,2292,2293,2295,2296,2297,2298,2300,2301,2302,2303,2304,2306,2308,2310,2312,2313,2314,2315,2316,2317,2318,2319,2320,2321,2322,2323,2324,2325,2326,2327", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "2859,5390,5439,14253,16112,16174,16238,16308,16369,16444,16520,16597,16675,16968,17050,17177,17253,17372,17450,17556,17662,17741,17821,17878,17936,18141,18216,18281,18347,18407,18468,18634,18707,18774,18842,18958,19017,19076,19135,19194,19248,19302,19355,19409,19463,19517,19571,19876,19955,20028,20102,20173,20245,20317,20390,20588,20646,20719,20793,20867,20942,21062,21135,21205,21276,21522,21583,21741,21810,21880,21954,22030,22094,22171,22247,22324,22389,22458,22535,22610,22679,22747,22824,22890,22951,23048,23113,23182,23281,23352,23411,23469,23526,23585,23649,23720,23792,23864,23936,24008,24075,24143,24211,24270,24333,24397,24487,24578,24638,24704,24771,24837,24907,24971,25024,25091,25152,25219,25332,25390,25453,25518,25583,25658,25731,25803,25847,25894,25940,25989,26050,26111,26172,26234,26298,26362,26426,26491,26554,26614,26675,26741,26800,26860,26922,26993,27053,27121,27695,27782,27872,27959,28047,28129,28212,28302,28393,29423,29481,29526,29592,29656,29713,29770,29824,32004,32052,32101,32152,32547,32881,32930,33083,33974,34407,34469,34529,34586,34803,34873,34951,35005,35075,35160,35208,35254,35315,35378,35444,35508,35579,35642,35707,35771,35832,35893,35945,36018,36092,36161,36236,36310,36384,36525,36595,42458,43065,43155,43243,43339,43429,44011,44100,44347,44628,44880,45165,45558,46035,46257,46479,46755,46982,47212,47442,47672,47902,48129,48548,48774,49199,49429,49857,50076,50359,50567,50698,50925,51351,51576,52003,52224,52649,52769,53045,53346,53670,53961,54275,54412,54543,54648,54890,55057,55261,55469,55740,55852,55964,56069,56186,56400,56546,56686,56772,57120,57208,57454,57872,58121,58203,58301,58958,59058,59310,59734,59989,60083,60172,60409,62433,62675,62777,63030,65186,75867,77383,88078,89606,91363,91989,92409,93670,94935,95191,95427,95974,96468,97073,97271,97851,99219,99594,99712,100250,100407,100603,100876,101132,101302,101443,101507,101872,102239,102915,103179,103517,103870,103964,104150,104456,104718,104843,104970,105209,105420,105539,105732,105909,106364,106545,106667,106926,107039,107226,107328,107435,107564,107839,108347,108843,109720,110014,110584,110733,111465,111637,111721,112057,112149,112427,118944,124315,124377,124955,125539,125630,125743,125972,126132,126284,126455,126621,126790,126957,127120,127363,127533,127706,127877,128151,128350,128555,128885,128969,129065,129161,129259,129359,129461,129563,129665,129767,129869,129969,130065,130177,130306,130429,130560,130691,130789,130903,130997,131137,131271,131367,131479,131579,131695,131791,131903,132003,132143,132279,132443,132573,132731,132881,133022,133166,133301,133413,133563,133691,133819,133955,134087,134217,134347,134459,134599,135503,135647,135785,135851,135941,136017,136121,136211,136313,136421,136529,136629,136709,136801,136899,137009,137061,137139,137245,137337,137441,137551,137673,137836,137993,138619,138719,138809,138919,139009,139250,139344,139450,139542,139642,139754,139868,139984,140100,140194,140308,140420,140522,140642,140764,140846,140950,141070,141196,141294,141388,141476,141588,141704,141826,141938,142113,142229,142315,142407,142519,142643,142710,142836,142904,143032,143176,143304,143373,143468,143583,143696,143795,143904,144015,144126,144227,144332,144432,144562,144653,144776,144870,144982,145068,145172,145268,145356,145474,145578,145682,145808,145896,146004,146104,146194,146304,146388,146490,146574,146628,146692,146798,146884,146994,147078,147198"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ffaf60804bb777caaab07cd66703845d\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,611,612,613,616,618,652,696,697,698,699,700,701,702,764,765,766,767,769,770,771,772,774,775,776,1867,1883,1886", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "30042,30101,30160,30220,30280,30340,30400,30460,30520,30580,30640,30700,30760,30819,30879,30939,30999,31059,31119,31179,31239,31299,31359,31419,31478,31538,31598,31657,31716,31775,31834,31893,32157,32231,32289,32467,32552,34295,37293,37358,37412,37478,37579,37637,37689,42190,42252,42306,42356,42463,42509,42555,42597,42708,42755,42791,112432,113412,113523", "endLines": "575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,611,612,613,616,618,652,696,697,698,699,700,701,702,764,765,766,767,769,770,771,772,774,775,776,1869,1885,1889", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "30096,30155,30215,30275,30335,30395,30455,30515,30575,30635,30695,30755,30814,30874,30934,30994,31054,31114,31174,31234,31294,31354,31414,31473,31533,31593,31652,31711,31770,31829,31888,31947,32226,32284,32339,32513,32602,34343,37353,37407,37473,37574,37632,37684,37744,42247,42301,42351,42405,42504,42550,42592,42632,42750,42786,42876,112539,113518,113713"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fe94774b27fdaa35093098e8a904795c\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "650", "startColumns": "4", "startOffsets": "34181", "endColumns": "49", "endOffsets": "34226"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fce910b49377672473080ebeba046646\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "777,778", "startColumns": "4,4", "startOffsets": "42881,42937", "endColumns": "55,54", "endOffsets": "42932,42987"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0b77f7f969be2bb725302f33ce1d15eb\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "626,648", "startColumns": "4,4", "startOffsets": "32935,34067", "endColumns": "41,59", "endOffsets": "32972,34122"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7b5fdb1008b0acd05f15a7bb1e11c858\\transformed\\constraintlayout-2.1.4\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,9,10,15,16,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,42,43,44,45,55,64,65,66,71,72,77,82,83,84,89,90,95,96,101,102,103,109,110,111,116,122,123,126,127,133,134,135,136,139,142,145,146,149,152,153,154,155,156,159,162,163,164,165,171,176,179,182,183,184,189,190,191,194,197,198,201,204,207,210,211,212,215,218,219,224,225,231,236,239,242,243,244,245,246,247,248,249,250,251,252,253,269,275,276,277,278,280,287,293,294,295,298,303,304,312,313,314,315,316,317,319,320,329,330,331,337,338,344,348,349,350,351,352,361,622,646", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,434,490,676,737,1028,1080,1130,1183,1231,1282,1337,1397,1462,1521,1583,1635,1696,1758,1804,1937,1989,2039,2090,2497,2864,2909,2968,3165,3222,3417,3598,3652,3709,3901,3959,4155,4211,4405,4462,4513,4735,4787,4842,5032,5248,5298,5444,5500,5706,5767,5827,5897,6030,6161,6289,6357,6486,6612,6674,6737,6805,6872,6995,7120,7187,7252,7317,7606,7787,7908,8029,8095,8162,8372,8441,8507,8632,8758,8825,8951,9078,9203,9330,9386,9451,9577,9700,9765,9973,10040,10328,10508,10628,10748,10813,10875,10937,11001,11063,11122,11182,11243,11304,11363,11423,12083,12334,12385,12434,12482,12600,12892,13122,13169,13229,13335,13515,13569,13904,13958,14014,14060,14107,14158,14258,14310,14640,14699,14753,14991,15046,15248,15387,15433,15488,15533,15577,15925,32731,33979", "endLines": "8,9,14,15,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,41,42,43,44,54,62,64,65,70,71,76,81,82,83,88,89,94,95,100,101,102,108,109,110,115,121,122,123,126,132,133,134,135,138,141,144,145,148,151,152,153,154,155,158,161,162,163,164,170,175,178,181,182,183,188,189,190,193,196,197,200,203,206,209,210,211,214,217,218,223,224,230,235,238,241,242,243,244,245,246,247,248,249,250,251,252,268,274,275,276,277,278,286,292,293,294,297,302,303,311,312,313,314,315,316,317,319,328,329,330,336,337,343,347,348,349,350,351,360,364,622,646", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44", "endOffsets": "429,485,671,732,1023,1075,1125,1178,1226,1277,1332,1392,1457,1516,1578,1630,1691,1753,1799,1932,1984,2034,2085,2492,2804,2904,2963,3160,3217,3412,3593,3647,3704,3896,3954,4150,4206,4400,4457,4508,4730,4782,4837,5027,5243,5293,5345,5495,5701,5762,5822,5892,6025,6156,6284,6352,6481,6607,6669,6732,6800,6867,6990,7115,7182,7247,7312,7601,7782,7903,8024,8090,8157,8367,8436,8502,8627,8753,8820,8946,9073,9198,9325,9381,9446,9572,9695,9760,9968,10035,10323,10503,10623,10743,10808,10870,10932,10996,11058,11117,11177,11238,11299,11358,11418,12078,12329,12380,12429,12477,12535,12887,13117,13164,13224,13330,13510,13564,13899,13953,14009,14055,14102,14153,14212,14305,14635,14694,14748,14986,15041,15243,15382,15428,15483,15528,15572,15920,16057,32767,34019"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fd1fb44d3025048634a00a9f3a07ea5c\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "615", "startColumns": "4", "startOffsets": "32401", "endColumns": "65", "endOffsets": "32462"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b53b0a6e7beae069ef1b2e945452fd0d\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "647", "startColumns": "4", "startOffsets": "34024", "endColumns": "42", "endOffsets": "34062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\dad7540d7a0d6788347b647b05ce89b9\\transformed\\fragment-1.3.6\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "614,627,651", "startColumns": "4,4,4", "startOffsets": "32344,32977,34231", "endColumns": "56,64,63", "endOffsets": "32396,33037,34290"}}]}]}