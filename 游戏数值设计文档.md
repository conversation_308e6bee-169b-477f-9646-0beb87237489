# 《雪山求生》游戏数值设计文档

## 1. 核心数值系统

### 1.1 显示数值（玩家可见）

#### 体温 (Warmth)
- **范围**: 0-100
- **初始值**: 80
- **作用**: 生命线，主要通过壁炉维持
- **死亡条件**: 降至0则游戏失败
- **恢复方式**: 夜晚木柴≥5时+10，木柴<5时-10

#### 体力 (Stamina)
- **范围**: 0-100
- **初始值**: 100
- **作用**: 行动力，执行大部分行动都需要消耗
- **死亡条件**: 降至0则游戏失败
- **恢复方式**:
  - 休息: +10到+20
  - 进食: +40（每次消耗1食物）
  - 夜晚结算: -10

#### 木柴 (Firewood)
- **范围**: 0-50
- **初始值**: 5
- **作用**: 热量来源，夜晚壁炉会自动消耗
- **获得方式**:
  - 劈开家具: +10（消耗体力-10，房屋完整度-5）
  - 外出砍柴: +15（消耗体温-15到-20，体力-20）
- **消耗方式**: 夜晚木柴≥5时-5

#### 食物 (Food)
- **范围**: 0-20
- **初始值**: 3
- **作用**: 体力来源，可在夜晚前选择进食来恢复体力
- **获得方式**:
  - 探索小屋: +2
  - 捕猎: +3（消耗体温-20，体力-30）
- **消耗方式**: 进食时-1，恢复体力+20

#### 当前天数 (Current Day)
- **范围**: 1-7+
- **初始值**: 1
- **作用**: 游戏目标是存活7天
- **胜利条件**: 存活超过7天

### 1.2 隐藏数值（玩家不可见）

#### 房屋完整度 (Cabin Integrity)
- **范围**: 0-100
- **初始值**: 40
- **作用**: 代表小屋的坚固程度，影响特殊事件触发
- **影响因素**:
  - 劈开家具: -5
  - 加固房屋: +15到+20（消耗体力和木柴）
- **特殊机制**: 当<50时触发"屋顶漏雪"事件，漏雪后每晚额外消耗10体温

#### 希望值 (Hope)
- **范围**: 0-100
- **初始值**: 50
- **作用**: 影响某些事件的文本描述和结局文本
- **影响因素**:
  - 积极行动（探索、发现物品）: +5
  - 消极行动（休息、等待）: -5到-10
  - 特殊事件: 可能大幅变化

#### 特殊物品 (Special Items)
- **类型**: 集合类型，存储获得的特殊物品
- **初始值**: 空集合
- **重要物品**:
  - 信号枪 (signal_gun): 第4天调查飞机残骸可获得，用于触发救援结局

## 2. 游戏流程规则

### 2.1 每日流程
1. **白天事件**: 玩家做出选择，影响数值
2. **进食阶段**: 夜晚前的独立必选事件
   - 有食物时询问是否进食
   - 选择"是": 食物-1，体力+20，显示状态变化
   - 可循环进食直到选择"否"或食物为0
   - 进食完成后进入夜晚
3. **夜晚阶段**: 自动结算
   - 体力-10
   - 木柴≥5: 木柴-5，体温+10
   - 木柴<5: 体温-10
4. **死亡检查**: 体温或体力≤0时游戏结束

### 2.2 数值约束规则
- 所有数值都有上下限约束
- 体温和体力降至0时立即触发游戏结束
- 木柴和食物有存储上限，防止无限囤积

## 3. 事件系统

### 3.1 事件类型分类

#### 线性事件
- **第1天**: day1_start → day1_evening → day1_night → day2_start
- **第2天**: day2_start → day2_night → day3_start
- **第3天**: day3_start → day3_night → day4_start
- **第4天**: day4_start → day4_night → day5_start
- **第5天**: day5_start → day5_night → day6_start
- **第6天**: day6_start → day6_night → day7_start
- **第7天**: day7_start → day7_night → 结局

#### 条件事件
- **屋顶检查** (roof_leak_check): 根据房屋完整度分支
  - 房屋完整度<50: 触发"屋顶漏雪"事件
  - 房屋完整度≥50: 触发"房屋状况良好"事件

#### 结局事件
- **救援结局** (rescue_ending): 拥有信号枪时可触发
- **生存结局** (survival_ending): 存活7天的默认结局

### 3.2 选择效果统计

#### 资源获取选择
- **劈开家具**: 体力-10，木柴+10，房屋完整度-5
- **外出砍柴**: 体温-15到-20，体力-20，木柴+15
- **探索小屋**: 体力-10，食物+2，希望+5
- **捕猎**: 体温-20，体力-30，食物+3
- **把所有能烧的都烧了**: 木柴+20，房屋完整度-20，希望+10

#### 防御性选择
- **加固房屋**: 体力-15到-20，木柴-3到-10，房屋完整度+15到+20
- **休息**: 体力不变，希望-5（某些情况）
- **修复屋顶**: 体力-20，木柴-10，房屋完整度+15
- **早点休息**: 体力+10，希望+5
- **蜷缩在壁炉边**: 体力+10，希望-5

#### 探险选择
- **调查飞机**: 体温-25，体力-30，获得信号枪
- **发射信号枪**: 希望+50，触发救援结局
- **待在屋里**: 希望-10（第4天选择）

#### 检查和维护选择
- **检查房屋状况**: 体力-10，触发条件事件
- **立刻修复屋顶**: 体力-20，木柴-10，房屋完整度+15
- **暂时不管屋顶**: 希望-10，设置屋顶漏雪状态（每晚额外消耗10体温）

### 3.3 选择需求条件
部分选择有前置条件：
- **外出砍柴**: 需要体力≥20
- **加固房屋**: 需要体力≥15，木柴≥3-5
- **捕猎**: 需要体力≥30
- **调查飞机**: 需要体力≥30
- **修复屋顶**: 需要体力≥20，木柴≥10
- **发射信号枪**: 需要拥有信号枪

## 4. 平衡性设计

### 4.1 生存难度曲线
- **第1-2天**: 相对容易，让玩家熟悉机制
- **第3-5天**: 难度上升，资源紧张
- **第6-7天**: 最高难度，考验资源管理

### 4.2 资源平衡
- **木柴**: 每晚消耗5，获取15，需要合理规划
- **食物**: 获取困难但恢复效果显著，需要谨慎使用
- **体力**: 消耗大但恢复方式多样，核心资源
- **体温**: 主要依赖木柴维持，最关键的生存指标

### 4.3 风险回报机制
- 高风险行动（外出、捕猎）提供更多资源
- 低风险行动（休息、等待）资源获取有限
- 特殊物品（信号枪）需要承担重大风险获取

## 5. 隐藏机制

### 5.1 房屋完整度影响
- 低于50时触发屋顶漏雪危机
- 影响后期生存难度
- 需要玩家在资源获取和房屋维护间平衡

### 5.2 希望值系统
- 影响事件文本的情感色彩
- 可能影响某些选择的可用性
- 为游戏增加心理层面的挑战

### 5.3 特殊物品机制
- 信号枪是唯一的"最佳结局"触发条件
- 获取需要在第4天做出高风险选择
- 增加游戏的重玩价值和策略深度

## 6. 数值调优建议

### 6.1 当前数值评估
- 夜晚体力消耗(-10)适中，保持紧张感
- 木柴消耗(5)与获取(10-15)比例合理
- 食物恢复效果(+40)显著，符合稀缺资源定位

### 6.2 潜在优化点
- 可考虑增加更多特殊物品和隐藏事件
- 希望值系统可以更深度地影响游戏体验
- 房屋完整度可以有更多层次的影响

这个数值系统通过多层次的资源管理、风险决策和隐藏机制，为玩家提供了丰富的策略选择和重玩价值。

## 7. 完整事件流程图

### 7.1 主线事件链
```
day1_start (第1天开始)
    ├─ 选择1: 劈开家具 → day1_evening
    └─ 选择2: 外出砍柴 → day1_evening

day1_evening (第1天黄昏)
    ├─ 选择1: 加固房屋 → day1_night
    └─ 选择2: 早点休息 → day1_night

day1_night (第1天夜晚) → day2_start

day2_start (第2天开始)
    ├─ 选择1: 探索小屋 → day2_night
    ├─ 选择2: 外出砍柴 → day2_night
    └─ 选择3: 休息保存体力 → day2_night

day2_night (第2天夜晚) → day3_start

day3_start (第3天开始)
    ├─ 选择1: 加固小屋 → day3_night
    ├─ 选择2: 尝试捕猎 → day3_night
    └─ 选择3: 继续休息 → day3_night

day3_night (第3天夜晚) → day4_start

day4_start (第4天开始)
    ├─ 选择1: 前往反光点调查 → crashed_plane
    └─ 选择2: 待在屋里 → day4_night

crashed_plane (坠毁的飞机)
    ├─ 选择1: 搜索驾驶舱 → day4_night (获得信号枪)
    └─ 选择2: 搜索货仓 → day4_night (食物+3)

day4_night (第4天夜晚) → day5_start

day5_start (第5天开始)
    ├─ 选择1: 检查房屋状况 → roof_leak_check
    └─ 选择2: 蜷缩在壁炉边 → day5_night

roof_leak_check (房屋状况检查)
    ├─ 房屋完整度<50 → roof_leak (屋顶漏雪)
    └─ 房屋完整度≥50 → house_ok (房屋良好)

roof_leak (屋顶漏雪)
    ├─ 选择1: 立刻修复 → day5_night
    └─ 选择2: 暂时不管 → day5_night

house_ok (房屋状况良好) → day5_night

day5_night (第5天夜晚) → day6_start

day6_start (第6天开始)
    ├─ 选择1: 把所有能烧的都烧了 → day6_evening
    └─ 选择2: 做最后的准备 → day6_evening

day6_evening (第6天黄昏) → day6_night

day6_night (第6天夜晚) → day7_start

day7_start (第7天开始)
    ├─ 选择1: 等待 → day7_evening
    └─ 选择2: 发射信号枪 → rescue_ending (需要信号枪)

day7_evening (第7天黄昏) → day7_night

day7_night (第7天夜晚) → survival_ending
```

### 7.2 进食事件流程
每天夜晚前都会触发进食事件：
```
进食询问
    ├─ 有食物且选择"是" → 食物-1，体力+40 → 继续询问
    ├─ 有食物且选择"否" → 进入夜晚
    └─ 无食物 → 直接进入夜晚
```

## 8. 技术实现细节

### 8.1 状态管理
- 使用 GameManager 单例管理所有游戏状态
- GameState 数据类包含所有数值
- 状态变化通过 applyEffects() 方法统一处理

### 8.2 事件系统
- 事件数据存储在 assets/events.json 文件中
- GameEngine 负责加载和解析事件数据
- 支持条件事件、随机事件和线性事件

### 8.3 UI更新机制
- MainActivity 通过 updateUI() 统一更新界面
- 使用动态规划方法管理UI状态转换
- 支持流式文本输出和按钮状态管理

### 8.4 数值约束
所有数值变化都通过 coerceIn() 方法确保在合理范围内：
- 体温: 0-100
- 体力: 0-100
- 木柴: 0-50
- 食物: 0-20
- 房屋完整度: 0-100
- 希望值: 0-100

## 9. 游戏策略分析

### 9.1 最优策略路径
1. **第1天**: 选择劈开家具（平衡风险），黄昏加固房屋
2. **第2天**: 探索小屋获得食物，为后续做准备
3. **第3天**: 根据体力情况选择捕猎或休息
4. **第4天**: 冒险调查飞机获得信号枪（关键选择）
5. **第5天**: 根据房屋状况决定是否修复
6. **第6天**: 保存体力准备最后阶段
7. **第7天**: 使用信号枪触发最佳结局

### 9.2 风险管理
- 始终保持体温和体力在安全线以上
- 合理分配木柴，确保每晚都有燃料
- 食物要在关键时刻使用，不要浪费
- 房屋完整度要在第5天前维持在50以上

### 9.3 资源优先级
1. **体温** - 最高优先级，关系生死
2. **木柴** - 维持体温的关键资源
3. **体力** - 执行行动的基础
4. **食物** - 紧急恢复体力的手段
5. **房屋完整度** - 影响特殊事件的隐藏数值

这个设计确保了游戏的挑战性、策略性和重玩价值，同时保持了合理的难度曲线。

## 10. 完整数值变化汇总表

### 10.1 所有选择的数值效果

| 选择名称 | 体温变化 | 体力变化 | 木柴变化 | 食物变化 | 房屋完整度 | 希望值变化 | 特殊效果 |
|---------|---------|---------|---------|---------|-----------|-----------|----------|
| 劈开旧家具生火 | 0 | -10 | +10 | 0 | -5 | 0 | 无 |
| 冒着风雪出去找柴火 | -20 | -20 | +15 | 0 | 0 | 0 | 无 |
| 加固房屋抵御风雪 | 0 | -15 | -3 | 0 | +15 | 0 | 需要木柴≥3，体力≥15 |
| 早点休息保存体力 | 0 | +10 | 0 | 0 | 0 | +5 | 无 |
| 探索小屋寻找物资 | 0 | -10 | 0 | +2 | 0 | +5 | 无 |
| 外出砍柴 | -15 | -20 | +15 | 0 | 0 | 0 | 需要体力≥20 |
| 休息以保存体力 | 0 | 0 | 0 | 0 | 0 | -5 | 无 |
| 加固小屋 | 0 | -15 | -5 | 0 | +20 | 0 | 需要体力≥15，木柴≥5 |
| 尝试捕猎 | -20 | -30 | 0 | +3 | 0 | 0 | 需要体力≥30 |
| 继续休息 | 0 | +20 | 0 | 0 | 0 | 0 | 无 |
| 前往反光点调查 | -25 | -30 | 0 | 0 | 0 | 0 | 需要体力≥30，进入飞机搜索 |
| 这是陷阱，待在屋里 | 0 | 0 | 0 | 0 | 0 | -10 | 无 |
| 搜索驾驶舱 | 0 | 0 | 0 | 0 | 0 | 0 | 获得信号枪 |
| 搜索货仓 | 0 | 0 | 0 | +3 | 0 | 0 | 无 |
| 检查房屋状况 | 0 | -10 | 0 | 0 | 0 | 0 | 触发条件事件 |
| 蜷缩在壁炉边 | 0 | +10 | 0 | 0 | 0 | -5 | 无 |
| 立刻修复屋顶 | 0 | -20 | -10 | 0 | +15 | 0 | 需要体力≥20，木柴≥10 |
| 暂时不管屋顶 | 0 | 0 | 0 | 0 | 0 | -10 | 设置屋顶漏雪状态 |
| 把所有能烧的都烧了 | 0 | 0 | +20 | 0 | -20 | +10 | 无 |
| 做最后的准备 | 0 | +10 | 0 | 0 | 0 | 0 | 无 |
| 发射信号枪 | 0 | 0 | 0 | 0 | 0 | +50 | 需要信号枪，触发救援结局 |

### 10.2 自动结算规则

#### 夜晚结算（每晚自动执行）
- **体力**: -10（固定消耗）
- **木柴和体温**:
  - 如果木柴≥5: 木柴-5，体温+10
  - 如果木柴<5: 体温-10
- **屋顶漏雪**: 如果屋顶漏雪状态为true，额外体温-10

#### 进食机制（夜晚前可选）
- **消耗**: 食物-1
- **恢复**: 体力+20
- **条件**: 必须有食物，可重复进食

### 10.3 死亡条件
- 体温≤0: 立即死亡
- 体力≤0: 立即死亡

### 10.4 胜利条件
- **救援结局**: 拥有信号枪并在第7天使用
- **生存结局**: 存活7天7夜（默认结局）

### 10.5 隐藏数值影响
- **房屋完整度<50**: 第5天触发"屋顶漏雪"危机事件
- **希望值**: 影响事件文本描述，可能影响某些选择的可用性
- **特殊物品**: 信号枪是触发最佳结局的唯一途径

这个完整的数值系统为玩家提供了丰富的策略选择空间，每个决定都会对后续的生存产生重要影响。
