package com.ainative.mountainsurvival

import org.junit.Test
import org.junit.Assert.*

/**
 * 基本功能测试
 * 验证核心类和数据结构的基本功能
 */
class BasicTest {

    @Test
    fun testGameStateCreation() {
        // 测试GameState创建
        val gameState = GameState()

        // 验证初始值
        assertEquals("初始体温应该为80", 80, gameState.warmth)
        assertEquals("初始体力应该为100", 100, gameState.stamina)
        assertEquals("初始食物应该为3", 3, gameState.food)
        assertEquals("初始木柴应该为5", 5, gameState.firewood)
        assertEquals("初始希望应该为50", 50, gameState.hope)
        assertEquals("初始房屋状况应该为40", 40, gameState.cabinIntegrity)
        assertEquals("初始天数应该为1", 1, gameState.currentDay)
        assertTrue("初始特殊物品应该为空", gameState.specialItems.isEmpty())
    }

    @Test
    fun testGameEventCreation() {
        // 测试GameEvent创建
        val choices = listOf(
            Choice(
                text = "测试选择",
                effects = mapOf("warmth" to -10, "stamina" to 5),
                resultText = "测试结果"
            )
        )

        val event = GameEvent(
            id = "test_event",
            text = "测试事件",
            choices = choices
        )

        assertEquals("事件ID应该正确", "test_event", event.id)
        assertEquals("事件文本应该正确", "测试事件", event.text)
        assertEquals("选择数量应该正确", 1, event.choices.size)
        assertEquals("选择文本应该正确", "测试选择", event.choices[0].text)
    }

    @Test
    fun testChoiceCreation() {
        // 测试Choice创建
        val choice = Choice(
            text = "测试选择",
            effects = mapOf("warmth" to -10, "stamina" to 5),
            resultText = "测试结果",
            requirements = mapOf("stamina" to 20)
        )

        assertEquals("选择文本应该正确", "测试选择", choice.text)
        assertEquals("效果数量应该正确", 2, choice.effects.size)
        assertEquals("体温效果应该正确", -10, choice.effects["warmth"])
        assertEquals("体力效果应该正确", 5, choice.effects["stamina"])
        assertEquals("结果文本应该正确", "测试结果", choice.resultText)
        assertEquals("要求数量应该正确", 1, choice.requirements?.size)
        assertEquals("体力要求应该正确", 20, choice.requirements?.get("stamina"))
    }

    @Test
    fun testRandomChoiceCreation() {
        // 测试RandomChoice创建
        val randomChoice = RandomChoice(
            chance = 0.5,
            nextEventId = "test_next_event"
        )

        assertEquals("概率应该正确", 0.5, randomChoice.chance, 0.001)
        assertEquals("下一个事件ID应该正确", "test_next_event", randomChoice.nextEventId)
    }

    @Test
    fun testEventContainerCreation() {
        // 测试EventContainer创建
        val events = listOf(
            GameEvent(
                id = "event1",
                text = "事件1",
                choices = emptyList()
            ),
            GameEvent(
                id = "event2",
                text = "事件2",
                choices = emptyList()
            )
        )

        val container = EventContainer(events)

        assertEquals("事件数量应该正确", 2, container.events.size)
        assertEquals("第一个事件ID应该正确", "event1", container.events[0].id)
        assertEquals("第二个事件ID应该正确", "event2", container.events[1].id)
    }

    @Test
    fun testBasicCalculations() {
        // 测试基本计算
        val result1 = 2 + 2
        val result2 = 10 - 5
        val result3 = 3 * 4
        val result4 = 15 / 3

        assertEquals("加法应该正确", 4, result1)
        assertEquals("减法应该正确", 5, result2)
        assertEquals("乘法应该正确", 12, result3)
        assertEquals("除法应该正确", 5, result4)
    }
}
