<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent_orange">#E67E22</color>
    <color name="background_light">#ECF0F1</color>
    <color name="black">#FF000000</color>
    <color name="firewood_color">#8B4513</color>
    <color name="food_color">#C0392B</color>
    <color name="ic_launcher_background">#114089</color>
    <color name="primary_blue">#2C3E50</color>
    <color name="primary_blue_dark">#1A252F</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="stamina_color">#27AE60</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="text_primary">#2C3E50</color>
    <color name="text_secondary">#95A5A6</color>
    <color name="warmth_color">#E67E22</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">雪山求生</string>
    <string name="app_name_en">MountainSurvival</string>
    <style name="Theme.MountainSurvival" parent="Theme.AppCompat.Light.NoActionBar">
        
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryDark">@color/primary_blue_dark</item>
        <item name="colorAccent">@color/accent_orange</item>

        
        <item name="android:windowBackground">@color/background_light</item>

        
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
    </style>
</resources>